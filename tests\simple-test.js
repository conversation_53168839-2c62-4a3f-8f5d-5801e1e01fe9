#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🧪 بدء اختبارات بسيطة لموقع البورتفوليو...\n');

// Test 1: Check if files exist
function testFilesExist() {
  console.log('📁 اختبار وجود الملفات...');
  
  const requiredFiles = [
    'index.html',
    'styles.css',
    'script.js'
  ];
  
  let allFilesExist = true;
  
  requiredFiles.forEach(file => {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
      console.log(`  ✅ ${file} موجود`);
    } else {
      console.log(`  ❌ ${file} غير موجود`);
      allFilesExist = false;
    }
  });
  
  return allFilesExist;
}

// Test 2: Check HTML structure
function testHTMLStructure() {
  console.log('\n🏗️  اختبار بنية HTML...');
  
  try {
    const htmlPath = path.join(__dirname, '..', 'index.html');
    const htmlContent = fs.readFileSync(htmlPath, 'utf8');
    
    const requiredElements = [
      { tag: '<nav', name: 'شريط التنقل' },
      { tag: '<section id="home"', name: 'قسم الرئيسية' },
      { tag: '<section id="about"', name: 'قسم نبذة عني' },
      { tag: '<section id="skills"', name: 'قسم المهارات' },
      { tag: '<section id="projects"', name: 'قسم المشاريع' },
      { tag: '<section id="contact"', name: 'قسم التواصل' },
      { tag: '<footer', name: 'التذييل' }
    ];
    
    let allElementsExist = true;
    
    requiredElements.forEach(element => {
      if (htmlContent.includes(element.tag)) {
        console.log(`  ✅ ${element.name} موجود`);
      } else {
        console.log(`  ❌ ${element.name} غير موجود`);
        allElementsExist = false;
      }
    });
    
    // Check for Arabic content
    const arabicPattern = /[\u0600-\u06FF]/;
    if (arabicPattern.test(htmlContent)) {
      console.log('  ✅ المحتوى العربي موجود');
    } else {
      console.log('  ❌ المحتوى العربي غير موجود');
      allElementsExist = false;
    }
    
    return allElementsExist;
  } catch (error) {
    console.log(`  ❌ خطأ في قراءة ملف HTML: ${error.message}`);
    return false;
  }
}

// Test 3: Check CSS structure
function testCSSStructure() {
  console.log('\n🎨 اختبار بنية CSS...');
  
  try {
    const cssPath = path.join(__dirname, '..', 'styles.css');
    const cssContent = fs.readFileSync(cssPath, 'utf8');
    
    const requiredStyles = [
      { selector: '.navbar', name: 'تنسيق شريط التنقل' },
      { selector: '.hero', name: 'تنسيق القسم الرئيسي' },
      { selector: '.about', name: 'تنسيق قسم نبذة عني' },
      { selector: '.skills', name: 'تنسيق قسم المهارات' },
      { selector: '.projects', name: 'تنسيق قسم المشاريع' },
      { selector: '.contact', name: 'تنسيق قسم التواصل' },
      { selector: '@media', name: 'التصميم المتجاوب' },
      { selector: 'background.*dark', name: 'الواجهة الداكنة' }
    ];
    
    let allStylesExist = true;
    
    requiredStyles.forEach(style => {
      const regex = new RegExp(style.selector, 'i');
      if (regex.test(cssContent)) {
        console.log(`  ✅ ${style.name} موجود`);
      } else {
        console.log(`  ❌ ${style.name} غير موجود`);
        allStylesExist = false;
      }
    });
    
    return allStylesExist;
  } catch (error) {
    console.log(`  ❌ خطأ في قراءة ملف CSS: ${error.message}`);
    return false;
  }
}

// Test 4: Check JavaScript structure
function testJavaScriptStructure() {
  console.log('\n⚡ اختبار بنية JavaScript...');
  
  try {
    const jsPath = path.join(__dirname, '..', 'script.js');
    const jsContent = fs.readFileSync(jsPath, 'utf8');
    
    const requiredFunctions = [
      { pattern: 'addEventListener', name: 'مستمعات الأحداث' },
      { pattern: 'querySelector', name: 'محددات العناصر' },
      { pattern: 'scroll', name: 'وظائف التمرير' },
      { pattern: 'click', name: 'وظائف النقر' },
      { pattern: 'form', name: 'معالجة النماذج' },
      { pattern: 'animation', name: 'الحركات والتأثيرات' }
    ];
    
    let allFunctionsExist = true;
    
    requiredFunctions.forEach(func => {
      const regex = new RegExp(func.pattern, 'i');
      if (regex.test(jsContent)) {
        console.log(`  ✅ ${func.name} موجود`);
      } else {
        console.log(`  ❌ ${func.name} غير موجود`);
        allFunctionsExist = false;
      }
    });
    
    return allFunctionsExist;
  } catch (error) {
    console.log(`  ❌ خطأ في قراءة ملف JavaScript: ${error.message}`);
    return false;
  }
}

// Test 5: Check file sizes
function testFileSizes() {
  console.log('\n📏 اختبار أحجام الملفات...');
  
  const files = [
    { name: 'index.html', maxSize: 50000 }, // 50KB
    { name: 'styles.css', maxSize: 100000 }, // 100KB
    { name: 'script.js', maxSize: 50000 } // 50KB
  ];
  
  let allSizesOk = true;
  
  files.forEach(file => {
    try {
      const filePath = path.join(__dirname, '..', file.name);
      const stats = fs.statSync(filePath);
      const sizeKB = (stats.size / 1024).toFixed(2);
      
      if (stats.size <= file.maxSize) {
        console.log(`  ✅ ${file.name}: ${sizeKB} KB (مقبول)`);
      } else {
        console.log(`  ⚠️  ${file.name}: ${sizeKB} KB (كبير نسبياً)`);
      }
    } catch (error) {
      console.log(`  ❌ خطأ في قراءة حجم ${file.name}`);
      allSizesOk = false;
    }
  });
  
  return allSizesOk;
}

// Test 6: Check for common issues
function testCommonIssues() {
  console.log('\n🔍 اختبار المشاكل الشائعة...');
  
  try {
    const htmlPath = path.join(__dirname, '..', 'index.html');
    const htmlContent = fs.readFileSync(htmlPath, 'utf8');
    
    let noIssues = true;
    
    // Check for missing alt attributes
    const imgTags = htmlContent.match(/<img[^>]*>/g) || [];
    imgTags.forEach((img, index) => {
      if (!img.includes('alt=')) {
        console.log(`  ⚠️  صورة ${index + 1} تفتقر لخاصية alt`);
        noIssues = false;
      }
    });
    
    // Check for missing meta viewport
    if (!htmlContent.includes('name="viewport"')) {
      console.log('  ⚠️  meta viewport مفقود');
      noIssues = false;
    } else {
      console.log('  ✅ meta viewport موجود');
    }
    
    // Check for charset
    if (!htmlContent.includes('charset="UTF-8"')) {
      console.log('  ⚠️  charset UTF-8 مفقود');
      noIssues = false;
    } else {
      console.log('  ✅ charset UTF-8 موجود');
    }
    
    // Check for lang attribute
    if (!htmlContent.includes('lang="ar"')) {
      console.log('  ⚠️  خاصية lang="ar" مفقودة');
      noIssues = false;
    } else {
      console.log('  ✅ خاصية lang="ar" موجودة');
    }
    
    if (noIssues) {
      console.log('  ✅ لا توجد مشاكل شائعة');
    }
    
    return noIssues;
  } catch (error) {
    console.log(`  ❌ خطأ في فحص المشاكل الشائعة: ${error.message}`);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 تشغيل جميع الاختبارات...\n');
  
  const tests = [
    { name: 'وجود الملفات', func: testFilesExist },
    { name: 'بنية HTML', func: testHTMLStructure },
    { name: 'بنية CSS', func: testCSSStructure },
    { name: 'بنية JavaScript', func: testJavaScriptStructure },
    { name: 'أحجام الملفات', func: testFileSizes },
    { name: 'المشاكل الشائعة', func: testCommonIssues }
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    const result = test.func();
    if (result) {
      passedTests++;
    }
  }
  
  console.log('\n📊 ملخص النتائج:');
  console.log(`✅ نجح: ${passedTests}/${totalTests} اختبار`);
  console.log(`❌ فشل: ${totalTests - passedTests}/${totalTests} اختبار`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 تهانينا! جميع الاختبارات نجحت!');
    console.log('موقع البورتفوليو جاهز للاستخدام! 🚀');
  } else {
    console.log('\n⚠️  هناك بعض المشاكل التي تحتاج إلى إصلاح.');
    console.log('يرجى مراجعة النتائج أعلاه وإصلاح المشاكل المذكورة.');
  }
  
  return passedTests === totalTests;
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ خطأ في تشغيل الاختبارات:', error);
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testFilesExist,
  testHTMLStructure,
  testCSSStructure,
  testJavaScriptStructure,
  testFileSizes,
  testCommonIssues
};