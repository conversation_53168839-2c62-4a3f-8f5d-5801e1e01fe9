const { test, expect } = require('@playwright/test');

test.describe('Basic Tests', () => {
  test('should load the portfolio website', async ({ page }) => {
    // Navigate to the local file
    await page.goto('file:///C:/Users/<USER>/OneDrive/Desktop/zack%20pro/index.html');
    
    // Check if page loads
    await expect(page.locator('title')).toContainText('بورتفوليو - زاك');
    
    // Check if main elements are visible
    await expect(page.locator('.navbar')).toBeVisible();
    await expect(page.locator('.hero')).toBeVisible();
    await expect(page.locator('.hero-title')).toBeVisible();
    
    console.log('✅ Basic test passed - Portfolio website loads successfully!');
  });

  test('should have correct page structure', async ({ page }) => {
    await page.goto('file:///C:/Users/<USER>/OneDrive/Desktop/zack%20pro/index.html');
    
    // Check sections exist
    const sections = ['#home', '#about', '#skills', '#projects', '#contact'];
    
    for (const section of sections) {
      await expect(page.locator(section)).toBeVisible();
    }
    
    console.log('✅ Page structure test passed - All sections are present!');
  });

  test('should have working navigation', async ({ page }) => {
    await page.goto('file:///C:/Users/<USER>/OneDrive/Desktop/zack%20pro/index.html');
    
    // Test navigation links
    const navLinks = page.locator('.nav-link');
    const count = await navLinks.count();
    
    expect(count).toBeGreaterThan(0);
    
    // Click on About link
    await page.click('a[href="#about"]');
    await page.waitForTimeout(1000);
    
    // Check if About section is in view
    const aboutSection = page.locator('#about');
    await expect(aboutSection).toBeInViewport();
    
    console.log('✅ Navigation test passed - Links work correctly!');
  });
});