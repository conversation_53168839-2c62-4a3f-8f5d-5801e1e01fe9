<testsuites id="" name="" tests="21" failures="21" skipped="0" errors="0" time="38.307949">
<testsuite name="basic.spec.js" timestamp="2025-07-20T08:09:12.985Z" hostname="chromium" tests="3" failures="3" skipped="0" time="0.036" errors="0">
<testcase name="Basic Tests › should load the portfolio website" classname="basic.spec.js" time="0.014">
<failure message="basic.spec.js:4:3 should load the portfolio website" type="FAILURE">
<![CDATA[  [chromium] › basic.spec.js:4:3 › Basic Tests › should load the portfolio website ─────────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1181\chrome-win\chrome.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have correct page structure" classname="basic.spec.js" time="0.011">
<failure message="basic.spec.js:19:3 should have correct page structure" type="FAILURE">
<![CDATA[  [chromium] › basic.spec.js:19:3 › Basic Tests › should have correct page structure ───────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1181\chrome-win\chrome.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have working navigation" classname="basic.spec.js" time="0.011">
<failure message="basic.spec.js:32:3 should have working navigation" type="FAILURE">
<![CDATA[  [chromium] › basic.spec.js:32:3 › Basic Tests › should have working navigation ───────────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1181\chrome-win\chrome.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="basic.spec.js" timestamp="2025-07-20T08:09:12.985Z" hostname="firefox" tests="3" failures="3" skipped="0" time="0.03" errors="0">
<testcase name="Basic Tests › should load the portfolio website" classname="basic.spec.js" time="0.011">
<failure message="basic.spec.js:4:3 should load the portfolio website" type="FAILURE">
<![CDATA[  [firefox] › basic.spec.js:4:3 › Basic Tests › should load the portfolio website ──────────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have correct page structure" classname="basic.spec.js" time="0.011">
<failure message="basic.spec.js:19:3 should have correct page structure" type="FAILURE">
<![CDATA[  [firefox] › basic.spec.js:19:3 › Basic Tests › should have correct page structure ────────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have working navigation" classname="basic.spec.js" time="0.008">
<failure message="basic.spec.js:32:3 should have working navigation" type="FAILURE">
<![CDATA[  [firefox] › basic.spec.js:32:3 › Basic Tests › should have working navigation ────────────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\firefox-1489\firefox\firefox.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="basic.spec.js" timestamp="2025-07-20T08:09:12.985Z" hostname="webkit" tests="3" failures="3" skipped="0" time="0.029" errors="0">
<testcase name="Basic Tests › should load the portfolio website" classname="basic.spec.js" time="0.009">
<failure message="basic.spec.js:4:3 should load the portfolio website" type="FAILURE">
<![CDATA[  [webkit] › basic.spec.js:4:3 › Basic Tests › should load the portfolio website ───────────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have correct page structure" classname="basic.spec.js" time="0.01">
<failure message="basic.spec.js:19:3 should have correct page structure" type="FAILURE">
<![CDATA[  [webkit] › basic.spec.js:19:3 › Basic Tests › should have correct page structure ─────────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have working navigation" classname="basic.spec.js" time="0.01">
<failure message="basic.spec.js:32:3 should have working navigation" type="FAILURE">
<![CDATA[  [webkit] › basic.spec.js:32:3 › Basic Tests › should have working navigation ─────────────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="basic.spec.js" timestamp="2025-07-20T08:09:12.985Z" hostname="Mobile Chrome" tests="3" failures="3" skipped="0" time="0.03" errors="0">
<testcase name="Basic Tests › should load the portfolio website" classname="basic.spec.js" time="0.011">
<failure message="basic.spec.js:4:3 should load the portfolio website" type="FAILURE">
<![CDATA[  [Mobile Chrome] › basic.spec.js:4:3 › Basic Tests › should load the portfolio website ────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1181\chrome-win\chrome.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have correct page structure" classname="basic.spec.js" time="0.009">
<failure message="basic.spec.js:19:3 should have correct page structure" type="FAILURE">
<![CDATA[  [Mobile Chrome] › basic.spec.js:19:3 › Basic Tests › should have correct page structure ──────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1181\chrome-win\chrome.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have working navigation" classname="basic.spec.js" time="0.01">
<failure message="basic.spec.js:32:3 should have working navigation" type="FAILURE">
<![CDATA[  [Mobile Chrome] › basic.spec.js:32:3 › Basic Tests › should have working navigation ──────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1181\chrome-win\chrome.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="basic.spec.js" timestamp="2025-07-20T08:09:12.985Z" hostname="Mobile Safari" tests="3" failures="3" skipped="0" time="0.032" errors="0">
<testcase name="Basic Tests › should load the portfolio website" classname="basic.spec.js" time="0.01">
<failure message="basic.spec.js:4:3 should load the portfolio website" type="FAILURE">
<![CDATA[  [Mobile Safari] › basic.spec.js:4:3 › Basic Tests › should load the portfolio website ────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have correct page structure" classname="basic.spec.js" time="0.011">
<failure message="basic.spec.js:19:3 should have correct page structure" type="FAILURE">
<![CDATA[  [Mobile Safari] › basic.spec.js:19:3 › Basic Tests › should have correct page structure ──────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have working navigation" classname="basic.spec.js" time="0.011">
<failure message="basic.spec.js:32:3 should have working navigation" type="FAILURE">
<![CDATA[  [Mobile Safari] › basic.spec.js:32:3 › Basic Tests › should have working navigation ──────────────

    Error: browserType.launch: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\webkit-2191\Playwright.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="basic.spec.js" timestamp="2025-07-20T08:09:12.985Z" hostname="Microsoft Edge" tests="3" failures="3" skipped="0" time="5.047" errors="0">
<testcase name="Basic Tests › should load the portfolio website" classname="basic.spec.js" time="1.704">
<failure message="basic.spec.js:4:3 should load the portfolio website" type="FAILURE">
<![CDATA[  [Microsoft Edge] › basic.spec.js:4:3 › Basic Tests › should load the portfolio website ───────────

    Error: browserContext.newPage: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\ffmpeg-1011\ffmpeg-win64.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have correct page structure" classname="basic.spec.js" time="1.855">
<failure message="basic.spec.js:19:3 should have correct page structure" type="FAILURE">
<![CDATA[  [Microsoft Edge] › basic.spec.js:19:3 › Basic Tests › should have correct page structure ─────────

    Error: browserContext.newPage: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\ffmpeg-1011\ffmpeg-win64.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have working navigation" classname="basic.spec.js" time="1.488">
<failure message="basic.spec.js:32:3 should have working navigation" type="FAILURE">
<![CDATA[  [Microsoft Edge] › basic.spec.js:32:3 › Basic Tests › should have working navigation ─────────────

    Error: browserContext.newPage: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\ffmpeg-1011\ffmpeg-win64.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
<testsuite name="basic.spec.js" timestamp="2025-07-20T08:09:12.985Z" hostname="Google Chrome" tests="3" failures="3" skipped="0" time="2.368" errors="0">
<testcase name="Basic Tests › should load the portfolio website" classname="basic.spec.js" time="1.05">
<failure message="basic.spec.js:4:3 should load the portfolio website" type="FAILURE">
<![CDATA[  [Google Chrome] › basic.spec.js:4:3 › Basic Tests › should load the portfolio website ────────────

    Error: browserContext.newPage: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\ffmpeg-1011\ffmpeg-win64.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have correct page structure" classname="basic.spec.js" time="0.803">
<failure message="basic.spec.js:19:3 should have correct page structure" type="FAILURE">
<![CDATA[  [Google Chrome] › basic.spec.js:19:3 › Basic Tests › should have correct page structure ──────────

    Error: browserContext.newPage: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\ffmpeg-1011\ffmpeg-win64.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
<testcase name="Basic Tests › should have working navigation" classname="basic.spec.js" time="0.515">
<failure message="basic.spec.js:32:3 should have working navigation" type="FAILURE">
<![CDATA[  [Google Chrome] › basic.spec.js:32:3 › Basic Tests › should have working navigation ──────────────

    Error: browserContext.newPage: Executable doesn't exist at C:\Users\<USER>\AppData\Local\ms-playwright\ffmpeg-1011\ffmpeg-win64.exe
    ╔═════════════════════════════════════════════════════════════════════════╗
    ║ Looks like Playwright Test or Playwright was just installed or updated. ║
    ║ Please run the following command to download new browsers:              ║
    ║                                                                         ║
    ║     npx playwright install                                              ║
    ║                                                                         ║
    ║ <3 Playwright Team                                                      ║
    ╚═════════════════════════════════════════════════════════════════════════╝
]]>
</failure>
</testcase>
</testsuite>
</testsuites>